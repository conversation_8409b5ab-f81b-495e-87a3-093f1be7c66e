{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__57DF517", "name": "智慧东大", "version": {"name": "3.0.6", "code": 2025051401}, "description": "智慧东大", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"FaceID": {}, "Fingerprint": {}, "Camera": {}, "Barcode": {}, "Push": {}, "Geolocation": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#3553A2"}, "compatible": {"ignoreVersion": true}, "usingComponents": true, "permissions": {"Invocation": {"description": "Native.js 调用权限"}}, "uts": true, "nativePlugins": {"Aliyun-Push": {"阿里云移动推送Android AppKey": "335343773", "阿里云移动推送Android AppSecret": "aa947b2ad1224fbe9ef798fc81c2efff", "阿里云移动推送iOS AppKey": "335343777", "阿里云移动推送iOS AppSecret": "a84ef780579c4e6da9adcf6bd9177f62", "__plugin_info__": {"name": "阿里云移动推送", "description": "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=7628", "android_package_name": "com.sunyt.testdemo", "ios_bundle_id": "cn.edu.neu.portal", "isCloud": true, "bought": 1, "pid": "7628", "parameters": {"阿里云移动推送Android AppKey": {"des": "阿里云EMAS移动应用标识", "key": "", "value": ""}, "阿里云移动推送Android AppSecret": {"des": "阿里云EMAS移动应用密钥", "key": "", "value": ""}, "阿里云移动推送iOS AppKey": {"des": "阿里云EMAS移动应用标识", "key": "aliyun:push:app<PERSON>ey", "value": ""}, "阿里云移动推送iOS AppSecret": {"des": "阿里云EMAS移动应用密钥", "key": "aliyun:push:appSecret", "value": ""}}}}, "My-Cookie": {"__plugin_info__": {"name": "MyC<PERSON>ie", "description": "Inject cookie into WKWebView on iOS", "platforms": "iOS", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}}, "uniStatistics": {"version": "2", "enable": false}, "kernel": {"ios": "WKWebview"}, "webview": {"wkWebView": {"allowsInlineMediaPlayback": true, "allowsLinkPreview": false, "allowsBackForwardNavigationGestures": true, "sharedCookies": true}}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.24", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#7A7E83", "selectedColor": "#007AFF", "borderStyle": "rgba(255,255,255,0.4)", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "static/ntab/tab_home_normal.png", "selectedIconPath": "static/ntab/tab_home_select.png", "text": "首页"}, {"pagePath": "pages/microapp", "iconPath": "static/ntab/tab_sw_normal.png", "selectedIconPath": "static/ntab/tab_sw_select.png", "text": "事务"}, {"pagePath": "pages/neunews", "iconPath": "static/ntab/tab_zx_normal.png", "selectedIconPath": "static/ntab/tab_zx_select.png", "text": "消息"}, {"pagePath": "pages/profile", "iconPath": "static/ntab/tab_my_normal.png", "selectedIconPath": "static/ntab/tab_my_select.png", "text": "我的"}], "height": "50px"}, "launch_path": "__uniappview.html"}}