<!DOCTYPE html>
<html lang="en">

	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width,initial-scale=1.0">
		<title>
			首页
		</title>
	</head>

	<body>

		<div id="timeShow" style="margin-top: 300px;" onclick="greet('2025-01-13 22:12:20')">开始</div>
		<script>
			function setCookie(tgt) {
				const expirationDate = new Date();
				expirationDate.setTime(expirationDate.getTime() + 2 * 60 * 60 * 1000);
				// const cookieValue = `CASTGC=${tgt}; expires=${expirationDate}; path=/tpass/; domain=pass.neu.edu.cn`
				 const cookieValue = `CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/; domain=pass.neu.edu.cn`
				// alert(tgt)
				document.cookie = cookieValue;
			}

			function getCookie() {
			}
		</script>
	</body>

</html>