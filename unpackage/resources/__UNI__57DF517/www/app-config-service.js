
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/login/login","pages/index/index","pages/message","pages/microapp","pages/neunews","pages/ecode","pages/profile","components/tabBar/tabBar","pages/webview/appwebview","pages/about/about","pages/privacy/privacy"],"window":{"navigationStyle":"default","navigationBarTextStyle":"white","navigationBarBackgroundColor":"#3553A2","onReachBottomDistance":20,"navigationBarTitleText":"一号通门户"},"tabBar":{"color":"#7A7E83","selectedColor":"#007AFF","borderStyle":"white","backgroundColor":"#ffffff","list":[{"pagePath":"pages/index/index","iconPath":"static/ntab/tab_home_normal.png","selectedIconPath":"static/ntab/tab_home_select.png","text":"首页"},{"pagePath":"pages/microapp","iconPath":"static/ntab/tab_sw_normal.png","selectedIconPath":"static/ntab/tab_sw_select.png","text":"事务"},{"pagePath":"pages/neunews","iconPath":"static/ntab/tab_zx_normal.png","selectedIconPath":"static/ntab/tab_zx_select.png","text":"消息"},{"pagePath":"pages/profile","iconPath":"static/ntab/tab_my_normal.png","selectedIconPath":"static/ntab/tab_my_select.png","text":"我的"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"weex","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"智慧东大","compilerVersion":"4.24","entryPagePath":"pages/login/login","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/login/login","meta":{"isQuit":true},"window":{"navigationBarTitleText":"登录","navigationStyle":"custom"}},{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"一号通门户","navigationStyle":"custom","enablePullDownRefresh":true}},{"path":"/pages/message","meta":{},"window":{"navigationBarTitleText":"消息服务"}},{"path":"/pages/microapp","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"事务中心"}},{"path":"/pages/neunews","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"消息中心"}},{"path":"/pages/ecode","meta":{},"window":{"navigationBarTitleText":"e码通"}},{"path":"/pages/profile","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":" ","backgroundColor":"#fff","enablePullDownRefresh":true}},{"path":"/components/tabBar/tabBar","meta":{},"window":{"navigationBarTitleText":"底部导航","enablePullDownRefresh":false,"backgroundColor":"#fff"}},{"path":"/pages/webview/appwebview","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/about/about","meta":{},"window":{"navigationBarTitleText":"关于我们"}},{"path":"/pages/privacy/privacy","meta":{},"window":{"navigationBarTitleText":"隐私协议"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
