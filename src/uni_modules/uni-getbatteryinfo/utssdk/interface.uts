type GetBatteryInfoResult = {
	/**
		* 错误码
		* 0:成功
		* 1001:getAppContext is null
		* 1002:navigator.getBattery is unsupported
		*/
	errCode : number,
	/**
		* 调用API的名称
		*/
	errSubject : string,
	/**
		* 错误的详细信息
		*/
	errMsg : string,
	/**
		* 设备电量，范围1 - 100
		*/
	level : number,
	/**
		* 是否正在充电中
		*/
	isCharging : boolean
}

interface UniError<T> {
	/**
		* 错误码
		*/
	errCode : number,
	/**
		* 调用API的名称
		*/
	errSubject : string,
	/**
		* 错误的详细信息
		*/
	errMsg : string,
	/**
		* 错误来源
		*/
	cause : any
}



export type GetBatteryInfoOptions = {
	/**
		* 接口调用结束的回调函数（调用成功、失败都会执行）
		*/
	success ?: (res : GetBatteryInfoResult) => void
	/**
		* 接口调用失败的回调函数
		*/
	fail ?: (res : UniError) => void
	/**
		* 接口调用成功的回调
		*/
	complete ?: (res : object) => void
}

/**
	* 获取电量信息
	* @param {GetBatteryInfoOptions} options
	* 
	*
	* @tutorial https://uniapp.dcloud.net.cn/api/system/batteryInfo.html
	* @platforms APP-IOS = ^9.0,APP-ANDROID = ^22
	* @since 3.6.11
	* 
	* @assert () => success({errCode: 0, errSubject: "uni-getBatteryInfo", errMsg: "getBatteryInfo:ok", level: 60, isCharging: false })
	* @assert () => fail({errCode: 1001, errSubject: "uni-getBatteryInfo", errMsg: "getBatteryInfo:fail getAppContext is null" })
	*/
export default function getBatteryInfo(options : GetBatteryInfoOptions) : void

