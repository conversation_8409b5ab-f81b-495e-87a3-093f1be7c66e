import { CookieManager } from 'cookieManager'

type CookieInfo = {
	domain : string,
	name : string,
	value : string,
	path : string
}

type successBlock = (resBool: boolean ) => void
const cookieManager = new CookieManager()

export function getNumber() : number {
	console.log("user:", cookieManager, cookieManager.getName())
	return 10
}
export function nativeSetCookie(options : CookieInfo, completeBlock: (res : boolean) => void) {
	cookieManager.setCookieUsingDefaultStore(
		name = options.name,
		value = options.value,
		domain = options.domain,
		path = options.path,
		completion = ( a , b ) => {
			console.log("结果:", a,b)
			// Dispatch.main.
			console.log("completeBlock___:", completeBlock)
		    completeBlock( a )
		},
	)
}
export function getNativeWebCookie(domain: string)  {
	cookieManager.getAllCookies(forDomain = domain,completion = ( res : any ) => {
		if(Array.isArray(res)){
			
			const cookieArray : HTTPCookie[] = res as Array<HTTPCookie>
			// for(let cookieItem of cookieArray){
			// 	 console.log("name=", cookieItem)
			// }
			cookieArray.forEach(( cookieItem: HTTPCookie, index: number ) => {
				console.log("cookieItem___:",cookieItem.name)
			})
			console.log("获取到的WebViewCookie是：", cookieArray)
			
		}
	})
}