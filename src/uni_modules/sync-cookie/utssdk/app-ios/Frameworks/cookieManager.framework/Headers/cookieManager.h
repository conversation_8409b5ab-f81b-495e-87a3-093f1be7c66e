//
//  cookieManager.h
//  cookieManager
//
//  Created by danda<PERSON> zhen<PERSON> on 2025/5/17.
//

#import <Foundation/Foundation.h>

//! Project version number for cookieManager.
FOUNDATION_EXPORT double cookieManagerVersionNumber;

//! Project version string for cookieManager.
FOUNDATION_EXPORT const unsigned char cookieManagerVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <cookieManager/PublicHeader.h>


