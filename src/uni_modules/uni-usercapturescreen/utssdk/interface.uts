/**
	* uni.onUserCaptureScreen/uni.offUserCaptureScreen回调参数
	*/
export type OnUserCaptureScreenCallbackResult = {
	/**
		* 截屏文件路径（仅Android返回）
		*/
	path ?: string
}

/**
	* uni.onUserCaptureScreen/uni.offUserCaptureScreen回调函数定义
	*/
export type UserCaptureScreenCallback = (res : OnUserCaptureScreenCallbackResult) => void

/**
	* uni.onUserCaptureScreen函数定义
	* 开启截屏监听 
	* 
	* @param {UserCaptureScreenCallback} callback 
	* @tutorial https://uniapp.dcloud.net.cn/api/system/capture-screen.html#onusercapturescreen
	* @platforms APP-IOS = ^9.0,APP-ANDROID = ^4.4
	* @since 3.7.7
	*/
export type OnUserCaptureScreen = (callback : UserCaptureScreenCallback | null) => void

/**
	* uni.offUserCaptureScreen函数定义
	* 关闭截屏监听  
	* 
	* @param {UserCaptureScreenCallback} callback 
	* @tutorial https://uniapp.dcloud.net.cn/api/system/capture-screen.html#offusercapturescreen
	* @platforms APP-IOS = ^9.0,APP-ANDROID = ^4.4
	* @since 3.7.7
	*/
export type OffUserCaptureScreen = (callback : UserCaptureScreenCallback | null) => void

/**
	* uni.setUserCaptureScreen成功回调参数
	*/
export type SetUserCaptureScreenSuccess = {
}

/**
	* uni.setUserCaptureScreen失败回调参数
	*/
export type SetUserCaptureScreenFail = {
	/**
		* 错误码
		* 12001:system not support
		* 12010:system internal error
		*/
	errCode : number,
	/**
		* 调用API的名称
		*/
	errSubject : string,
	/**
		* 错误的详细信息
		*/
	errMsg : string,
}

/**
	* uni.setUserCaptureScreen成功回调函数定义
	*/
export type SetUserCaptureScreenSuccessCallback = (res : SetUserCaptureScreenSuccess) => void

/**
	* uni.setUserCaptureScreen失败回调函数定义
	*/
export type SetUserCaptureScreenFailCallback = (res : SetUserCaptureScreenFail) => void

/**
	* uni.setUserCaptureScreen完成回调函数定义
	*/
export type SetUserCaptureScreenCompleteCallback = (res : any) => void

/**
	* uni.setUserCaptureScreen参数
	*/

export type SetUserCaptureScreenOptions = {
	/**
	* true: 允许用户截屏 false: 不允许用户截屏，防止用户截屏到应用页面内容
	*/
	enable : boolean;
	/**
	* 接口调用成功的回调函数
	*/
	// success : SetUserCaptureScreenSuccessCallback | null,
	success ?: SetUserCaptureScreenSuccessCallback,
	/**
	* 接口调用失败的回调函数
	*/
	// fail : SetUserCaptureScreenFailCallback | null,
	fail ?: SetUserCaptureScreenFailCallback,
	/**
	* 接口调用结束的回调函数（调用成功、失败都会执行）
	*/
	// complete : SetUserCaptureScreenSuccessCallback | SetUserCaptureScreenFailCallback | null
	complete ?: SetUserCaptureScreenCompleteCallback
}


/**
 * * uni.setUserCaptureScreen函数定义

	* 设置防截屏  
	* 
	* @param {SetUserCaptureScreenOptions} options 
	* @tutorial https://uniapp.dcloud.net.cn/api/system/capture-screen.html#setusercapturescreen
	* @platforms APP-IOS = ^13.0,APP-ANDROID = ^4.4
	* @since 3.7.7
	*/
export type SetUserCaptureScreen = (options : SetUserCaptureScreenOptions) => void

export interface Uni {
	onUserCaptureScreen : OnUserCaptureScreen,
	offUserCaptureScreen : OffUserCaptureScreen,
	setUserCaptureScreen : SetUserCaptureScreen
}