# uni-usercapturescreen

用户主动截屏事件监听

### uni.onUserCaptureScreen

监听用户主动截屏事件，用户使用系统截屏按键截屏时触发此事件。

> 使用文档：[https://uniapp.dcloud.net.cn/api/system/capture-screen.html#onusercapturescreen](https://uniapp.dcloud.net.cn/api/system/capture-screen.html#onusercapturescreen)

### uni.offUserCaptureScreen

用户主动截屏事件。取消事件监听。

> 使用文档：[https://uniapp.dcloud.net.cn/api/system/capture-screen.html#offusercapturescreen](https://uniapp.dcloud.net.cn/api/system/capture-screen.html#offusercapturescreen)

### uni.setUserCaptureScreen

开启/关闭防截屏。

> 使用文档：[https://uniapp.dcloud.net.cn/api/system/capture-screen.html#setusercapturescreen](https://uniapp.dcloud.net.cn/api/system/capture-screen.html#setusercapturescreen)
