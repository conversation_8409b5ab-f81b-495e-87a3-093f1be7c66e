import { request } from "../utils/request";
import env from "../env";

const IDENTITY_BASE_URL = "https://pass.neu.edu.cn/tpass/identity";

// 存储临时的temp_token
let tempParams = {};

/**
 * 获取设备ID
 * @returns {Promise<string>} 返回设备ID
 */
async function getDeviceId() {
  // #ifdef APP-PLUS
  const deviceInfo = await new Promise((resolve) => {
    plus.device.getInfo({
      success: (res) => {
        console.log("res____:", res);
        resolve(res);
      },
      fail: () => resolve({}),
    });
  });
  return deviceInfo.uuid || `web_${Math.random().toString(36).substr(2, 9)}`;
  // #endif
}

/**
 * 获取客户端IP
 * @returns {Promise<string>} 返回客户端IP
 */
async function getClientIP() {
  // #ifdef APP-PLUS
  try {
    const ipAddress = await new Promise((resolve) => {
      uni.request({
        url: "https://ipinfo.io/ip",
        success: (res) => {
          const publicIP = res.data;
          resolve(publicIP);
        },
      });
    });
    return ipAddress;
  } catch (e) {
    console.error("获取IP失败:", e);
    return "127.0.0.1";
  }
  // #endif
}

/**
 * 获取TGT(Ticket Granting Ticket)
 * @param {Object} params - 请求参数
 * @param {string} params.username - 用户名
 * @param {string} params.password - 密码
 * @param {number} [params.expires_in=3600] - 过期时间(秒)，默认3600秒
 * @returns {Promise<Object>} Promise对象，解析后包含TGT信息
 */
export async function getTGT(params) {
  try {
    console.log(params);

    const device_id = await getDeviceId();
    const client_ip = await getClientIP();
    console.log("clinet_ip", client_ip);
    const response = await request.middleware({
      baseURL: IDENTITY_BASE_URL,
      url: `/tickets`,
      method: "POST",
      data: {
        username: params.username,
        password: params.password,
        expires_in: params.expires_in || 3600,
        device_id,
        client_ip,
      },
    });

    return response;
  } catch (error) {
    console.error("获取TGT失败:", error);
    throw error;
  }
}

/**
 * 获取ST(Service Ticket)
 * @param {Object} params - 请求参数
 * @param {string} params.tgt - TGT值
 * @param {string} params.service - 服务URL
 * @returns {Promise<string>} Promise对象，解析后为ST值
 */
export async function getST(params) {
  try {
    const device_id = await getDeviceId();
    const client_ip = await getClientIP();

    const response = await request.middleware({
      url: `/tickets/${encodeURIComponent(params.tgt)}`,
      method: "POST",
      data: {
        service: params.service,
        device_id,
        client_ip,
      },
    });

    return response.data; // 假设ST直接返回在响应体中
  } catch (error) {
    console.error("获取ST失败:", error);
    throw error;
  }
}

export async function getDeviceIdClientIP() {
     
	const device_id = await getDeviceId();
	const client_ip = await getClientIP();

	tempParams = {
		device_id,
		client_ip,
	};
   console.log("tempParams", tempParams);
}
/**
 * 发送短信验证码
 * @param {Object} credentials - 认证信息
 * @param {string} credentials.username - 用户名
 * @param {string} credentials.password - 密码
 * @param {number} [expiresIn=300] - 过期时间(秒)，默认300秒
 * @returns {Promise<Object>} Promise对象，包含发送结果
 */
export async function sendSmsVerification(credentials, expiresIn = 300) {
  try {
    // 显示加载中
    // uni.showLoading({
    //   title: '正在获取验证码...',
    //   mask: true
    // });
    // 1. 首先获取TGT和temp_token
    const tgtResponse = await getTGT({
      username: credentials.username,
      password: credentials.password,
      expires_in: expiresIn,
    });
    // 检查是否成功获取到temp_token
    if (tgtResponse.code !== 3 || !tgtResponse.info) {
      throw new Error(tgtResponse.info || "获取验证码失败，请重试");
    }

    const temp_token = tgtResponse.info;
    tempParams.temp_token = temp_token;

    // 2. 使用获取到的temp_token发送短信
    const response = await request.middleware({
      baseURL: IDENTITY_BASE_URL,
      url: `/resend`,
      method: "POST",
      data: {
        temp_token,
        device_id: tempParams.device_id,
        client_ip: tempParams.client_ip,
      },
    });

    // 隐藏加载中
    uni.hideLoading();

    // 检查短信发送结果
    if (response.success) {
      uni.showToast({
        title: "验证码发送成功",
        icon: "success",
        duration: 2000,
      });
    } else {
      throw new Error(response.info || "验证码发送失败，请重试");
    }

    return response;
  } catch (error) {
    // 隐藏加载中
    uni.hideLoading();

    // 显示错误提示
    uni.showModal({
      title: "提示",
      content: error.message || "发送验证码失败，请检查网络或稍后重试",
      showCancel: false,
    });

    console.error("发送短信验证码失败:", error);
    throw error;
  }
}

/**
 * 验证短信验证码
 * @param {Object} params - 请求参数
 * @param {string} params.temp_token - 临时令牌
 * @param {string} params.sms_code - 短信验证码
 * @param {number} params.expires_in - 过期时间(秒)
 * @param {boolean} [params.trust_device=false] - 是否信任设备
 * @param {string} params.device_id - 设备ID
 * @param {string} params.client_ip - 客户端IP
 * @returns {Promise} Promise对象
 */
export function verifySmsCode(params) {
  const { temp_token, device_id, client_ip } = tempParams;
  const data = {
    temp_token,
    sms_code: params.sms_code,
    expires_in: 3600,
    trust_device: params.trust_device || false,
    device_id,
    client_ip,
  };
  console.log("temp_token", data);
  return request.middleware({
    baseURL: IDENTITY_BASE_URL,
    url: `/verify`,
    method: "POST",
    data,
  });
}

export function login(data) {
  return request.middleware({
    baseURL: env.api.baseURL,
    url: "/Front/Oauth/User/sso",
    method: "POST",
    data,
  });
}

export function checkVersionInfo(data) {
  return request.middleware({
    baseURL: env.api.baseURL,
    url: "/Front/Neu/VersionInfo/get",
    method: "POST",
    data,
  });
}

export function getLoginType() {
  return request.middleware({
    baseURL: env.api.baseURL,
    url: "/Front/Oauth/User/login_type",
    method: "POST"
  });
}

export function smsSend( data ) {
  return request.middleware({
    baseURL: env.api.baseURL,
    url: "/Front/Oauth/User/sms_send",
    method: "POST",
    data:{
      ...data,
      device_id: tempParams.device_id,
    }
  });
}

export function smsVerify( data ) {
  
  return request.middleware({
    baseURL: env.api.baseURL,
    url: "/Front/Oauth/User/sms_verify",
    method: "POST",
    data:{
      ...data,
      device_id: tempParams.device_id,
    }
  });
}

export function smsLogin( data ) {
  return request.middleware({
    baseURL: env.api.baseURL,
    url: "/Front/Oauth/User/login_sms",
    method: "POST",
    data:{
      ...data,
      device_id: tempParams.device_id,
    }
  });
}

