import {
	request
} from "../utils/request";
import env from "../env";

const IDENTITY_BASE_URL = "https://pass.neu.edu.cn/tpass/identity";

// 存储临时的temp_token
let tempParams = {};

function generateDeviceId(seed = '') {
	let h = 2166136261 >>> 0;
	for (let i = 0; i < seed.length; i++) {
		h ^= seed.charCodeAt(i);
		h = (h * 16777619) >>> 0;
	}
	let hex = (h >>> 0).toString(16).padStart(8, '0');
	for (let r = 1; r <= 3; r++) {
		h = (h * 1664525 + 1013904223 + r) >>> 0; 
		hex += (h >>> 0).toString(16).padStart(8, '0');
	}
	return hex; 
}

function getDeviceId( un ) {
	const deviceInfo = uni.getDeviceInfo()
	// return deviceInfo.deviceId;
	const userName = uni.getStorageSync('userName') || un
	const seed =
		`${deviceInfo.brand}_${deviceInfo.deviceModel}_${deviceInfo.osVersion}_${userName}`
	console.log("seed___:", seed)
	const cDeviceId = generateDeviceId(seed)
	console.log("c_:", cDeviceId)
	return cDeviceId;
}

export function checkVersionInfo(data) {
	return request.middleware({
		baseURL: env.api.baseURL,
		url: "/Front/Neu/VersionInfo/get",
		method: "POST",
		data,
	});
}

export function smsReSend(data) {
	return request.middleware({
		baseURL: env.api.baseURL,
		url: "/Front/Oauth/User/sms_resend",
		method: "POST",
		data: {
			...data,
			device_id: getDeviceId(),
		}
	});
}
export function smsVerify(data) {
	const _data = {
		...data,
		device_id: getDeviceId(),
	}
	return request.middleware({
		baseURL: env.api.baseURL,
		url: "/Front/Oauth/User/sms_verify",
		method: "POST",
		data: {
			...data,
			device_id: getDeviceId(),
		}
	});
}
// 2d2547bc63447fec0fed7a5d9d54381b
export function smsLogin(data) {
	return request.middleware({
		baseURL: env.api.baseURL,
		url: "/Front/Oauth/User/login_sms",
		method: "POST",
		data: {
			...data,
			device_id: getDeviceId( data.username ),
		}
	});
}