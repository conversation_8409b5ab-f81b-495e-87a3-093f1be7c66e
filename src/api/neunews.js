import { request } from '../utils/request'
import qs from "qs";

// 新闻列表
export function getNeunewsArticleList (params) {
  return request.get('/neunews/articles' + '?' + qs.stringify(params, {skipNulls: true}))
}

// 新闻来源列表
export function getNeunewsSites (params) {
	return request.get('/neunews/sites' + '?' + qs.stringify(params, {skipNulls: true}))
}

// 订阅新闻列表
export function getSubscriptionsList (params) {
	return request.get('/neunews/subscriptions'+ '?' + qs.stringify(params, {skipNulls: true}))
}

// 订阅
export function postSubscriptions (data) {
  //#ifdef APP-PLUS
  request.config.header["X-XSRF-TOKEN"] = uni.getStorageSync('X-XSRF-TOKEN')
  request.config.header["Content-Type"] = 'application/vnd.api+json'
  //#endif
  return request.middleware({
    url: '/neunews/subscriptions',
    method: 'POST',
    data: JSON.stringify(data)
  })
}
// 取消订阅
export function deleteSubscriptions (id) {
  //#ifdef APP-PLUS
  request.config.header["X-XSRF-TOKEN"] = uni.getStorageSync('X-XSRF-TOKEN')
  request.config.header["Content-Type"] = 'application/vnd.api+json'
  //#endif
  return request.middleware({
    url: '/neunews/subscriptions/' + id,
    method: 'DELETE'
  })
}