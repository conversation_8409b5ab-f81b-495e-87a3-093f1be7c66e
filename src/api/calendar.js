import { request } from '../utils/request';
import qs from "qs";
// import qs from "qs";

// <!-- sources相关接口 -->
// 获取日程分类（列表）
export function getCalendarSources () {
  return request.middleware({
    url: '/calendar/sources',
    method: 'GET'
  })
}

// 新增日程分类（新增）
export function postCalendarSources (data) {
  return request.middleware({
    url: '/calendar/sources',
    method: 'POST',
    data: JSON.stringify(data)
  })
}

// 删除日程分类（删除）
export function deleteCalendarSources (id) {
  return request.middleware({
    url: '/calendar/sources/' + id,
    method: 'DELETE'
  })
}

// 更新日程分类（修改）
export function putCalendarSource (id, data) {
  return request.middleware({
    url: '/calendar/sources/' + id,
    method: 'PUT',
    data: JSON.stringify(data)
  })
}

// <!-- events相关接口 -->

// 获取日程（查询）
export function getCalendarEvents (params) {
  return request.get('/calendar/events' + '?' + qs.stringify(params, {skipNulls: true}))
}

// 获取某个日程（查制定id日程）
export function getCalendarEvent (id) {
  return request.middleware({
    url: '/calendar/events/' + id,
    method: 'GET'
  })
}

// 新增日程（新增）
export function postCalendarEvent (data) {
  return request.middleware({
    url: '/calendar/events',
    method: 'POST',
    data: JSON.stringify(data)
  })
}

// 删除日程（删除）
export function deleteCalendarEvent (id) {
  return request.middleware({
    url: '/calendar/events/' + id,
    method: 'DELETE'
  })
}

// 修改日程（修改）
export function putCalendarEventItem (id,data) {
  return request.middleware({
    url: '/calendar/events/' + id,
    method: 'PUT',
    data: JSON.stringify(data)
  })
}

// <!-- notifications相关接口 -->

// 查询提醒
export function getCalendarNotificationList (params) {
  return request.get('/calendar/notifications' + '?' + qs.stringify(params, {skipNulls: true}))
}

// 新增提醒
export function postCalendarNotificationItem (data) {
  return request.middleware({
    url: '/calendar/notifications',
    method: 'POST',
    data: JSON.stringify(data)
  })
}

// 删除提醒
export function deleteCalendarNotificationItem (id) {
  return request.middleware({
    url: '/calendar/notifications/' + id,
    method: 'DELETE'
  })
}
