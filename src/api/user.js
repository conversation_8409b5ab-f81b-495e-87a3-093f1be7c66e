import { request } from '../utils/request';
import qs from "qs";

// 获取用户信息
export function user () {
  return request.middleware({
    url: '/user',
    method: 'GET'
  })
}

// 获取用户信息
export function userinfo () {
  return request.middleware({
    url: '/user/info',
    method: 'GET'
  })
}

// 邮箱
export function getMail () {
  return request.get('/personal/campusInfo/mail')
}

// 网络
export function getNetwork () {
  return request.get('/personal/campusInfo/network')
}

// 校园卡
export function getCard () {
  return request.get('/personal/campusInfo/card')
}

// 报销
export function getFinance () {
  return request.get('/personal/campusInfo/finance')
}
// 图书
export function getLibrary () {
  return request.get('/personal/campusInfo/library')
}


// 上传头像（新增）
export function postFiles (data) {
  return request.middleware({
    url: '/customer/files',
    method: 'POST',
    data: JSON.stringify(data)
  })
}

export function updateFile (data) {
  //#ifdef APP-PLUS
  request.config.header["X-XSRF-TOKEN"] = uni.getStorageSync('X-XSRF-TOKEN')
  request.config.header["Content-Type"] = 'application/vnd.api+json'
  //#endif
  return request.middleware({
    url: '/user',
    method: 'PUT',
    data: JSON.stringify(data)
  })
}