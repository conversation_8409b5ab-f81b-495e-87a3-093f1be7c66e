import { request } from '../utils/request'
import qs from "qs";

// 获取消息列表
export function getMessageList (params) {
  return request.get('/message/notifications' + '?' + qs.stringify(params, {skipNulls: true}))
}
// 获取消息
export function getMessage (id) {
  return request.get('/message/notifications/' + id)
}

//修改消息为已读
export function putMessage (data,id) {
	//#ifdef APP-PLUS
	request.config.header["X-XSRF-TOKEN"] = uni.getStorageSync('X-XSRF-TOKEN')
	request.config.header["Content-Type"] = 'application/vnd.api+json'
	//#endif
    return request.middleware({
		url: '/message/notifications/' + id,
		method: 'PUT',
		data:JSON.stringify(data)
  })
}

// 获取显示数据数量
export function getSourceList (data) {
  return request.middleware({
    url: '/message/sources/sourceList',
    method: 'GET',
	data: data
  })
}
