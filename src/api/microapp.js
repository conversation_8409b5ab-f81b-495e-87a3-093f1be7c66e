import { request } from '../utils/request'
import qs from "qs";

// 当前应用列表
export function getMicroappApplicationList (params) {
  return request.get('/microapp/applications' + '?' + qs.stringify(params, {skipNulls: true}))
}

// 当前分类列表
export function getMicroappCategoryList () {
  return request.middleware({
    url: '/microapp/categories',
    method: 'GET'
  })
}

// 收藏的应用
export function getMicroappFavoriteList (params) {
	return request.get('/microapp/favorites' + '?' + qs.stringify(params, {skipNulls: true}))
}

// 收藏（新增）
export function postFavorites (data) {
  //#ifdef APP-PLUS
  request.config.header["X-XSRF-TOKEN"] = uni.getStorageSync('X-XSRF-TOKEN')
  request.config.header["Content-Type"] = 'application/vnd.api+json'
  //#endif
  return request.middleware({
    url: '/microapp/favorites',
    method: 'POST',
    data: JSON.stringify(data)
  })
}

// 取消收藏
export function deleteFavorites (id) {
  console.log(id)
  //#ifdef APP-PLUS
  request.config.header["X-XSRF-TOKEN"] = uni.getStorageSync('X-XSRF-TOKEN')
  request.config.header["Content-Type"] = 'application/vnd.api+json'
  //#endif
  return request.middleware({
    url: '/microapp/favorites/' + id,
    method: 'DELETE'
  })
}