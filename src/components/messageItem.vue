<template>
	<view>
    <checkbox-group @change="checkboxChange">
      <view class="content-warp" v-for="item in $props.data" >
      	<label :style="{'display': isHome === 'home' ? 'none' :''}">
      		<checkbox :value="item.id" :checked="allCheck === 'Y' && item.attributes.status !== 0"  :disabled="item.attributes.status === 0" style="transform:scale(0.6)"/>
      	</label>
        <view  @click="send(item.id,item.attributes.status)" style="display:flex;">
            <view class="circle" :style="{'opacity':item.attributes.status === 1 ? '100' : '0'}"></view>
            <view class="right">
              <view class="top">{{ item.attributes.data }}</view>
                <view class="bottom">
                  <view class="source">{{ getName(item.attributes.sourceId) }}</view>
                  <view class="time">{{ item.attributes.createTime }}</view>
                </view>
              </view>
          </view>
        </view>
    </checkbox-group>
	</view>
</template>

<script>
	export default {
		name:"messageItem",
		props: {
		  data: {
				type: Array
			},
			sourceList: {
				type: Array
			},
      isHome: {
        type: String
      },
      allCheck: {
        type: String
      }
		},
		methods: {
			getName(id) {
				let name = ''
				this.$props.sourceList.forEach((item) => {
				  if (Number(item.id) === Number(id)) {
				    name = item.name
				  }
				})
				return name
			},
			send(id,status) {
				if (status===1) {
					this.$emit('send',id)
				}
			},
      checkboxChange(e) {
        let value = e.target.value
        this.$emit('send',value)
      }
		}
	}
</script>

<style>
.content-warp {
	background-color: #fff;
	border-radius: 20rpx;
	padding:20rpx;
	margin: 20rpx;
	font-size: 28rpx;
	display: flex;
}
.circle {
	width: 10rpx;
	height: 10rpx;
	background-color: red;
	border-radius: 50%;
	margin-top: 20rpx;
	margin-right: 10rpx;
}
.right {
  flex: 1 
}
.top {
	margin-bottom: 10rpx;
	line-height: 40rpx;
	word-break:break-all;
}
.bottom {
	display:flex;
	justify-content: space-between;
	color: #c8c8c8;
}
.bottom .source,.bottom .time {
  font-size: 24rpx;
}
</style>
