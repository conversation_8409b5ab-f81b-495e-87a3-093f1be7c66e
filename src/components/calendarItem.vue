<template>
	<view>
		<view class="content-warp" v-for="item in $props.data">
			<view class="circle" :style="'background:' + item.attributes.color"></view>
			<view class="right" @click="goUrl('https://portal.neu.edu.cn/mobile/#/pages/calendar/calendar')">
				<view class="top">{{ item.attributes.title }}</view>
        <view class="bottom" style="word-break: break-all;">{{ item.attributes.description }}</view>
				<view class="bottom">{{ item.attributes.start + ' - ' + item.attributes.end }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"calendarItem",
		props: {
		  data: {
				type: Array
			},
      type: {
        type: String
      }
		},
    methods: {
      goUrl(url) {
        let type = this.$props.type
        if (type === 'index') {
          uni.navigateTo({
          	url:'./../webView?url='+url
          })
        } else {
          console.log("type",type)
        }
      },
    }
	}
</script>

<style>
.content-warp {
	background-color: #fff;
	border-radius: 20rpx;
	padding:20rpx;
	margin: 20rpx;
	font-size: 28rpx;
	display: flex;
}
.circle {
	width: 10rpx;
	height: 10rpx;
    background: #3788d8;
	border-radius: 50%;
	margin-top: 12rpx;
	margin-right: 10rpx;
}
.top {
	margin-bottom: 10rpx;
	line-height: 40rpx;
	word-break:break-all;
}
.bottom {
	color: #c8c8c8;
  font-size: 24rpx;
}
</style>
