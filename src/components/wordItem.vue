<template>
	<view>
		<view class="word-warp" v-for="item in $props.data">
			<view class="word-status">
				<view class="wait" v-if="item.attributes.status === 0">待办</view>
				<view class="finish" v-else>已办</view>
			</view>
			<view class="word-content">
				<view class="title">{{ item.attributes.title }}</view>
				<view class="source">{{ getName(item.attributes.sourceId) }}</view>
				<view class="time">{{ item.attributes.status === 0 ? item.attributes.startTime : item.attributes.finishTime}}</view>
			</view>
			<view class="word-url">
				<view class="handle" v-if="item.attributes.status === 0" @click="toUrl(item.attributes.url)">办理 &gt;&gt;</view>
				<view class="see" v-else @click="toUrl(item.attributes.url)">查看 &gt;&gt;</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"wordItem",
		props: {
		    data: {
				  type: Array
			},
            sourceList: {
                type: Array
            }
		},
    methods: {
      toUrl(url) { // 跳转到办理事务页面
        uni.navigateTo({
          url:'./../webView?url='+ encodeURIComponent(url)
        })
      },
      getName(id) { // 获取来源名称
        let name = ''
        this.$props.sourceList.forEach((item) => {
          if (Number(item.id) === Number(id)) {
            name = item.attributes.name
          }
        })
        return name
      }
    }
	}
</script>

<style>
.word-warp {
	background-color: #fff;
	display: flex;
	border-radius: 20rpx;
	padding:20rpx;
	margin: 20rpx;
	font-size: 28rpx;
}
.word-status {
	flex: 1;
	color: #fff;
	margin-right: 20rpx;
}
.word-status .wait {
	background-color: #ff6b7e;
	text-align: center;
	border-radius: 20rpx;
	height: 40rpx;
	line-height: 40rpx;
}
.word-status .finish {
	background-color: #c1c1c1;
	text-align: center;
	border-radius: 20rpx;
	height: 40rpx;
	line-height: 40rpx;
}
.word-content {
	flex: 4;
	margin-right: 20rpx;
}
.word-content view{
	margin-bottom: 6rpx;
	color:#d5d5d5;
}
.word-content .title{
	color: #000;
	line-height: 40rpx;
	word-break:break-all;
}
.word-content .source{
	color:#d5d5d5;
	font-size: 24rpx;
}
.word-content .time{
	color:#d5d5d5;
	font-size: 24rpx;
}
.word-url {
	flex: 1
}
.word-url .handle {
	color: #3f6dc3;
}
.word-url .see {
	color: #6b6b6b;
}
</style>
