<template>
	<view class="tabber-warp">
		<view class="tabbar">
		    <view class="tabbar-item"
		        v-for="(item, index) in list"
		        :key="index"
		        @click="tabbarChange(item.path)"
		    >
				<view>
					<image :class="index === 2 ? 'item-img-top item-img' : 'item-img'" :src="item.icon_a" v-if="current == index"></image>
					<image :class="index === 2 ? 'item-img-top item-img' : 'item-img'" :src="item.icon" v-else></image>
					<view :class="index === 2 ? 'item-name-top' : current == index ? 'item-name tabbarActive' : 'item-name'" v-if="item.text">{{item.text}}</view>
				</view>
		    </view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'tabBar',
    props: {
      current: {
			  type: Number
		  },
    },
    data() {
        return {
            list: [{
                    text: '首页',
                    icon: '/static/home.png',  //未选中图标
                    icon_a: '/static/home.png',  //选中图片
                    path: "/pages/index/index",  //页面路径
                },{
                    text: '资讯',
                    icon: '/static/news.png',  //未选中图标
                    icon_a: '/static/news.png',  //选中图片
                    path: "/pages/neunews",  //页面路径
                },{
                    text: 'e码通',
                    icon: '/static/e.png',  //未选中图标
                    icon_a: '/static/e.png',  //选中图片
                    path: "/pages/ecode",  //页面路径
                },{
                    text: '应用',
                    icon: '/static/apps.png',  //未选中图标
                    icon_a: '/static/apps.png',  //选中图片
                    path: "/pages/microapp",  //页面路径
                },{
                    text: '我的',
                    icon: '/static/my.png',  //未选中图标
                    icon_a: '/static/my.png',  //选中图片
                    path: "/pages/profile",  //页面路径
                }
            ],
        };
    },
    methods: {
      tabbarChange(path) {
        this.$emit('send',path)
        uni.switchTab({
          url: path
        })
      }
    }
};
</script>
<style>
	.tabber-warp {
		position: fixed;
		bottom: 0;
	}
	.tabbar {
		display: flex;
		justify-content: center;
		flex-grow: 5;
		width: calc(100vw - 80rpx);
		padding: 0 40rpx;
		height: 100rpx;
		background-color: #3D8EFE;
		border-radius: 30px 30px 0 0;
		text-align: center;
	}
	.tabbar-item {
		flex: 1
	}
	.item-img {
		width: 50rpx;
		height: 50rpx;
		vertical-align: middle;
		margin-top: 6rpx;
		margin-bottom: 6rpx;
	}
	.item-img-top {
		width: 100rpx;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 50%;
		position: relative;
		bottom: 40rpx;
		text-align: center;
	}
	.item-name-top {
		font-size: 24rpx;
		color: #fff;
		position: relative;
		bottom: 50rpx;
	}
	.item-name {
		font-size: 24rpx;
		color: #fff;
	}
</style>
