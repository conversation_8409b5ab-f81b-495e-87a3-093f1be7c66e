{
    "name" : "智慧东大",
    "appid" : "__UNI__57DF517",
    "description" : "智慧东大",
    "versionName" : "3.0.13",
    "versionCode" : 2025082101,
    "transformPx" : false,
    "app-plus" : {
        "debug" : true,
        "error" : {
            "url" : "hybrid/html/error.html"
        },
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持
        },
        /* 5+App特有相关 */
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "permissions" : {
            "Invocation" : {
                "description" : "Native.js 调用权限"
            }
        },
        "uts" : true,
        "modules" : {
            "Camera" : {},
            "Barcode" : {},
            "Push" : {},
            "Geolocation" : {},
            "Bluetooth" : {},
            "iBeacon" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCOUNT_MANAGER\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.USE_FINGERPRINT\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"com.heytap.mcss.permission.PUSH_MESSAGE\" />",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS\"/>",
                    "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "schemes" : "neuapp"
            },
            "ios" : {
                "dSYMs" : false,
                "idfa" : false,
                "deploymentTarget" : "13.0",
                "capabilities" : {
                    "Webkit" : {}
                },
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "该应用需要读取你的相册，选择图像以便用于扫一扫识别二维码信息。",
                    "NSCameraUsageDescription" : "该应用需要调用你的摄像头，以便识别二维码信息。",
                    "NSPhotoLibraryAddUsageDescription" : "该应用需要读取你的相册，以便用于二维码图片存储。",
                    "NSLocationWhenInUseUsageDescription" : "该应用需要根据位置判断是否在指定区域。",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "该应用需要根据位置判断是否在指定区域。",
                    "NSLocationAlwaysUsageDescription" : "该应用需要根据位置判断是否在指定区域。"
                },
                "urltypes" : "neuapp"
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "maps" : {},
                "payment" : {},
                "push" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "nativePlugins" : {
            "Aliyun-Push" : {
                "阿里云移动推送Android AppKey" : "335343773",
                "阿里云移动推送Android AppSecret" : "aa947b2ad1224fbe9ef798fc81c2efff",
                "阿里云移动推送iOS AppKey" : "335343777",
                "阿里云移动推送iOS AppSecret" : "a84ef780579c4e6da9adcf6bd9177f62",
                "__plugin_info__" : {
                    "name" : "阿里云移动推送",
                    "description" : "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=7628",
                    "android_package_name" : "com.sunyt.testdemo",
                    "ios_bundle_id" : "cn.edu.neu.portal",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "7628",
                    "parameters" : {
                        "阿里云移动推送Android AppKey" : {
                            "des" : "阿里云EMAS移动应用标识",
                            "key" : "",
                            "value" : ""
                        },
                        "阿里云移动推送Android AppSecret" : {
                            "des" : "阿里云EMAS移动应用密钥",
                            "key" : "",
                            "value" : ""
                        },
                        "阿里云移动推送iOS AppKey" : {
                            "des" : "阿里云EMAS移动应用标识",
                            "key" : "aliyun:push:appKey",
                            "value" : ""
                        },
                        "阿里云移动推送iOS AppSecret" : {
                            "des" : "阿里云EMAS移动应用密钥",
                            "key" : "aliyun:push:appSecret",
                            "value" : ""
                        }
                    }
                }
            },
            "Aliyun-ThirdPush" : {
                "com.gcm.push.apiKey" : "",
                "com.gcm.push.applicationid" : "",
                "com.gcm.push.projectid" : "",
                "com.gcm.push.sendid" : "",
                "com.hihonor.push.app_id" : "",
                "com.huawei.hms.client.appid" : "1597946820544190144",
                "com.meizu.push.id" : "",
                "com.meizu.push.key" : "",
                "com.oppo.push.key" : "3093c676c4e543388afbc51900b60baa",
                "com.oppo.push.secret" : "8b451f00a8c24c4aafde1df7e68029df",
                "com.vivo.push.api_key" : "",
                "com.vivo.push.app_id" : "",
                "com.xiaomi.push.id" : "2882303761520258127",
                "com.xiaomi.push.key" : "5912025872127",
                "__plugin_info__" : {
                    "name" : "阿里云移动推送-厂商通道",
                    "description" : "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。厂商通道是使用手机厂商提供的",
                    "platforms" : "Android",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=7629",
                    "android_package_name" : "com.sunyt.testdemo",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "7629",
                    "parameters" : {
                        "com.gcm.push.apiKey" : {
                            "des" : "gcm推送apiKey",
                            "key" : "",
                            "value" : ""
                        },
                        "com.gcm.push.applicationid" : {
                            "des" : "gcm推送applicationId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.gcm.push.projectid" : {
                            "des" : "gcm推送projectid",
                            "key" : "",
                            "value" : ""
                        },
                        "com.gcm.push.sendid" : {
                            "des" : "gcm推送sendId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.hihonor.push.app_id" : {
                            "des" : "荣耀推送AppId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.huawei.hms.client.appid" : {
                            "des" : "华为推送AppId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.meizu.push.id" : {
                            "des" : "魅族推送ID",
                            "key" : "",
                            "value" : ""
                        },
                        "com.meizu.push.key" : {
                            "des" : "魅族推送Key",
                            "key" : "",
                            "value" : ""
                        },
                        "com.oppo.push.key" : {
                            "des" : "Oppo推送Key",
                            "key" : "",
                            "value" : ""
                        },
                        "com.oppo.push.secret" : {
                            "des" : "Oppo推送密钥",
                            "key" : "",
                            "value" : ""
                        },
                        "com.vivo.push.api_key" : {
                            "des" : "vivo推送Api Key",
                            "key" : "",
                            "value" : ""
                        },
                        "com.vivo.push.app_id" : {
                            "des" : "vivo推送App Id",
                            "key" : "",
                            "value" : ""
                        },
                        "com.xiaomi.push.id" : {
                            "des" : "小米推送ID",
                            "key" : "",
                            "value" : ""
                        },
                        "com.xiaomi.push.key" : {
                            "des" : "小米推送Key",
                            "key" : "",
                            "value" : ""
                        }
                    }
                }
            }
        },
        "uniStatistics" : {
            "enable" : true
        },
        "kernel" : {
            "ios" : "WKWebview"
        },
        "webview" : {
            "wkWebView" : {
                "allowsInlineMediaPlayback" : true,
                "allowsLinkPreview" : false,
                "allowsBackForwardNavigationGestures" : true,
                "sharedCookies" : true
            }
        }
    },
    /* SDK配置 */
    "h5" : {
        "publicPath" : "./",
        "router" : {
            "base" : "./"
        },
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 微信小程序特有相关 */
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "mp-qq" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "version" : "2"
    },
    "vueVersion" : "2"
}
/* ios打包配置 */

