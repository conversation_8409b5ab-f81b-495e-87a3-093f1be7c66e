{
    "name" : "智慧东大",
    "appid" : "__UNI__57DF517",
    "description" : "智慧东大",
    "versionName" : "3.0.18",
    "versionCode" : 2025090908,
    "transformPx" : false,
    "app-plus" : {
        "debug" : true,
        "error" : {
            "url" : "hybrid/html/error.html"
        },
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持
        },
        /* 5+App特有相关 */
        "usingComponents" : true,
        "splashscreen" : {
            // "alwaysShowBeforeRender" : true,
            // "waiting" : false,
            // "autoclose" : false,
            // "delay" : 0
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : true,
            "delay" : 2
        },
        "permissions" : {
            "Invocation" : {
                "description" : "Native.js 调用权限"
            }
        },
        "uts" : true,
        "modules" : {
            "Camera" : {},
            "Barcode" : {},
            "Geolocation" : {},
            "Bluetooth" : {},
            "iBeacon" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCOUNT_MANAGER\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"com.heytap.mcss.permission.PUSH_MESSAGE\" />",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS\"/>",
                    "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "schemes" : "neuapp",
                "minSdkVersion" : 26,
                "targetSdkVersion" : 30,
                "renderMode" : "stream"
            },
            "ios" : {
                "dSYMs" : false,
                "idfa" : false,
                "deploymentTarget" : "13.0",
                "capabilities" : {
                    "Webkit" : {}
                },
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "该应用需要读取你的相册，选择图像以便用于扫一扫识别二维码信息。",
                    "NSCameraUsageDescription" : "该应用需要调用你的摄像头，以便识别二维码信息。",
                    "NSPhotoLibraryAddUsageDescription" : "该应用需要读取你的相册，以便用于二维码图片存储。",
                    "NSLocationWhenInUseUsageDescription" : "该应用需要根据位置判断是否在指定区域。",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "该应用需要根据位置判断是否在指定区域。",
                    "NSLocationAlwaysUsageDescription" : "该应用需要根据位置判断是否在指定区域。"
                },
                "urltypes" : "neuapp"
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "maps" : {},
                "payment" : {},
                "push" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "nativePlugins" : {},
        "uniStatistics" : {
            "enable" : true
        },
        "kernel" : {
            "ios" : "WKWebview"
        },
        "webview" : {
            "wkWebView" : {
                "allowsInlineMediaPlayback" : true,
                "allowsLinkPreview" : false,
                "allowsBackForwardNavigationGestures" : true,
                "sharedCookies" : true
            }
        }
    },
    /* SDK配置 */
    "h5" : {
        "publicPath" : "./",
        "router" : {
            "base" : "./"
        },
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 微信小程序特有相关 */
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "mp-qq" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "version" : "2"
    },
    "vueVersion" : "2"
}
/* ios打包配置 */

