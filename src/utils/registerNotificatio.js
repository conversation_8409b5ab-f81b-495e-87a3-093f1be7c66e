export function registerPushNotifaication() {
    return
	const platform = uni.getSystemInfoSync().platform;
	if (!['ios', 'android'].includes(platform) || plus.device.model.includes("Simulator")) return;
	const aliyunPush = uni.requireNativePlugin('<PERSON>yun-Push');
	const deviceResult = aliyunPush.getDeviceId();
    console.log("deviceResutl___:", deviceResult)
	const userName = uni.getStorageSync('userName')

	if (platform == 'ios') {
		aliyunPush.addAlias({
			alias: userName
		}, result => {

		});
		//通知回调
		aliyunPush.setNotificationCallback({}, result => {
			console.log("setNotificationCallback", result);
		});
		//用户点击
		aliyunPush.setNotificationResponseCallback({}, result => {
			console.log("setNotificationResponseCallback", result);
		});
		//消息
		aliyunPush.setMessageCallback({}, result => {
			console.log("setMessageCallback", result);
		});

	} else if (platform == 'android') {
		const channel = uni.requireNativePlugin('<PERSON>yun-Push-NotificationChannel');
		const aliyunThirdPush = uni.requireNativePlugin('Aliyun-ThirdPush')
		aliyunThirdPush.registerLog({}, result => {
			console.warn("third push plugin log : " + result);
		})
		// aliyunThirdPush.registerThirdPush({}, result => {
		// 	const data = JSON.stringify(result);
		// 	console.log("receive third push : " + data);
		// });
		const channelRes = channel.isNotificationEnabled({
			id: 'neu_channel'
		});
		channel.createChannel({
			id: 'neu_channel',
			name: 'NEU安卓通知',
			desc: 'NEU安卓通知通道',
			importance: 3,
		});
		aliyunPush.registerLog({}, result => {
			console.warn("push plugin log : " + result);
		});
		aliyunPush.registerPush({}, result => {

			const event = result.event;
			if (event === 'registerPush') {
				const userName = uni.getStorageSync('userName')
				aliyunPush.addAlias({
					alias: userName
				}, result => {
					console.log("result___:", result)
					// this.handleResult('addAlias', result);
				});
				if (result.code === 'success') {
					console.log("注册推送 成功 ");
				} else {
					console.log("注册推送 " + result.code + " " + result.msg);
				}
			} else {
				const data = JSON.stringify(result.data);
				console.log("receive push event : " + event);
				console.log("receive push data : " + data);
			}
		});

	}
}