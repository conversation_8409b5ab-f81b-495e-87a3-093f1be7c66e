export function jsDateFormatter (date,type) {
  let str
  let data = new Date(date)
  let seperator = "-";
  let seperator2 = ":";
  let month = data.getMonth() + 1;
  let strDate = data.getDate();
  let strMin = data.getMinutes()
  let strHours = data.getHours()
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  if (strMin >= 0 && strMin <= 9) {
    strMin = "0" + strMin;
  }
  if (strHours === 0) {
    strHours = "00";
  }
  if (type === 'start') {
    str = data.getFullYear() + seperator + month + seperator + strDate + " " + strHours + seperator2 + strMin + seperator2 + '00'
  } else if (type === 'end'){
    str = data.getFullYear() + seperator + month + seperator + strDate + " " + strHours + seperator2 + strMin + seperator2 + '59'
  } else if (type === 'start-index') {
    str = data.getFullYear() + seperator + month + seperator + strDate + " " + '00:00:00'
  } else if (type === 'end-index') {
    str = data.getFullYear() + seperator + month + seperator + strDate + " " + '23:59:59'
  } else if (type === 'alldata') {
	  str = data.getFullYear() + seperator + month + seperator + strDate
  }
  return str;
}
