    export checkVersion() {
		const _this = this;
		plus.runtime.getProperty(plus.runtime.appid, (appInfo) => {
			// 获取版本号
			const currentVersion = appInfo.version;
			// 这是用来看看版本是多少的
			_this.getVersionFn(currentVersion);
		});
	},
	compareVersion(v1, v2) {
		const version1 = v1.split(".").map(Number);
		const version2 = v2.split(".").map(Number);

		for (let i = 0; i < version1.length; i++) {
			if (version1[i] < version2[i]) {
				return -1;
			} else if (version1[i] > version2[i]) {
				return 1;
			}
		}
		return 0;
	},
	async getVersionFn(currentVersion) {
			const systemInfo = uni.getSystemInfoSync();
			// 0 是安卓  1 是iOS
			const os = systemInfo.platform == "android" ? "0" : "1";
			const res = await checkVersionInfo({
				os: os,
			});
			//非强制更新且用户点击过取消
			const cancleClick = uni.getStorageSync("updateCancle");
			if (res.result.is_update == 0 && cancleClick == 1) {
				return;
			}
			const updateUrl = res.result.link; // 下载地址
			const latestVersion = res.result.number; // 版本号
			console.log(
				"线上版本：",
				latestVersion,
				this.compareVersion(currentVersion, latestVersion)
			);
			// 比较版本号，判断是否有新版本
			if (this.compareVersion(currentVersion, latestVersion) < 0) {
				// 提示用户更新
				uni.showModal({
					title: "版本更新",
					content: res.result.desc,
					showCancel: res.result.is_update != 1,
					cancelText: "暂不更新",
					confirmText: "立即更新",
					success: (modalRes) => {
						if (modalRes.confirm) {
							if (os == 0) {
								// 用户确认更新，下载新版本
								this.downloadAndInstall(updateUrl);
							} else {
								let appleId = 1454919505;
								plus.runtime.launchApplication({
										action: `itms-apps://itunes.apple.com/cn/app/id${appleId}?mt=8`,
									},
									function(e) {
										console.log(
											"Open system default browser failed: " + e.message
										);
									}
								);
							}
						} else if (modalRes.cancel) {
							uni.setStorageSync("updateCancle", 1);
						}
					},
				});
			} else {
				console.log("当前已是最新版本");
			}
		},
		downloadAndInstall(url) {
			var dtask = plus.downloader.createDownload(url, {}, function(d, status) {
				// 下载完成
				if (status == 200) {
					plus.runtime.install(
						plus.io.convertLocalFileSystemURL(d.filename), {}, {},
						function(error) {
							uni.showToast({
								title: "安装失败",
								duration: 1500,
							});
						}
					);
				} else {
					uni.showToast({
						title: "更新失败",
						duration: 1500,
					});
				}
			});
			try {
				dtask.start(); // 开启下载的任务
				var prg = 0;
				var showLoading = plus.nativeUI.showWaiting("正在下载"); //创建一个showWaiting对象
				dtask.addEventListener("statechanged", function(task, status) {
					// 给下载任务设置一个监听 并根据状态  做操作
					switch (task.state) {
						case 1:
							showLoading.setTitle("正在下载");
							break;
						case 2:
							showLoading.setTitle("已连接到服务器");
							break;
						case 3:
							prg = parseInt(
								(parseFloat(task.downloadedSize) / parseFloat(task.totalSize)) *
								100
							);
							showLoading.setTitle("  正在下载" + prg + "%  ");
							break;
						case 4:
							plus.nativeUI.closeWaiting();
							//下载完成
							break;
					}
				});
			} catch (err) {
				plus.nativeUI.closeWaiting();
				uni.showToast({
					title: "更新失败",
					mask: false,
					duration: 1500,
				});
			}
		},