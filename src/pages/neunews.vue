<template>
	<view>
		<web-view :src="webUrl"></web-view>
	</view>
</template>

<script>
	export default {
		name: "neunews",
		data() {
			return {
               webUrl:"https://personal.neu.edu.cn/portal/ucs/pages/ucs/site/messageCenter/m_home?type=3"
			}
		},
		onShow(){
		   
		   uni.setNavigationBarTitle({
		   	title: "消息中心"
		   })
		},
		onLoad() {
			// uni.showLoading({
			// 	title:"加载中..."
			// })
			// var currentWebview = this.$scope.$getAppWebview();
			// setTimeout(function() {
			// 	var wv = currentWebview.children()[0];
			// 	wv.addEventListener('loaded', function(e) {
			// 		console.log("消息中心加载完成____:loaded:")
					
			// 	})
			
			// }, 2000)

		},
	

	}
</script>

<style scoped>

</style>