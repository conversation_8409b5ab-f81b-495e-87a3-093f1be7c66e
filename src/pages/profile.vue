<template>
	<view>
		<web-view :src="webUrl" @message="onMessageClick"></web-view>
	</view>
</template>

<script>
	import {
		login,
		checkVersionInfo,
		sendSmsVerification,
		verifySmsCode,
		getDeviceIdClientIP,
		getLoginType,
		smsSend,
		smsVerify,
		smsLogin,
	} from "@/api/login";
	import {
		request
	} from "@/utils/request";
	
	import {
		getNumber,
		nativeSetCookie
	} from "@/uni_modules/sync-cookie";
	export default {
		name: "neunews",
		data() {
			return {
			   webUrl: "https://personal.neu.edu.cn/prize/fe/square/userMy",
			}
		},
		
		mounted () {
			
			const isIOS = uni.getSystemInfoSync().platform == "ios";
			const _this = this;
			const currentWebview = _this.$scope.$getAppWebview();
			plus.nativeUI.showWaiting();
			const timer = setInterval(function() {
				const wv = currentWebview.children()[0];
				uni.hideKeyboard();
				if (wv) {
					
					clearInterval(timer);
					wv.addEventListener('loading', (e) => {
						const url = wv.getURL();
						plus.nativeUI.showWaiting()
						wv.setStyle({
							width: "1px",
						});
					});
					wv.addEventListener("loaded", function() {
						_this.wv = wv;
						if (wv.getURL().includes("/tpass/login")) {
							if (wv.getURL().includes("webvpn.neu.edu.cn")) {
								plus.nativeUI.closeWaiting()
								uni.reLaunch({
									url: "/pages/login/login"
								})
								return
							}
							uni.hideKeyboard();
							const userName = uni.getStorageSync("userName");
							const userPsw = uni.getStorageSync("userPsw");
							const rememberPsw = uni.getStorageSync("rememberPsw");
							if (userName && userPsw) {
								const data = {
									username: userName,
									password: userPsw,
								};
								smsLogin(data).then((res) => {
									plus.nativeUI.closeWaiting();
									if (res.code == 1) {
										_this.onSmsVerify(res.result);
									} else if (res.code == 2 || res.code == 4) {
										uni.showToast({
											title: res.msg,
											icon: "none",
										});
									} else if (res.code == 3) {
										plus.nativeUI.closeWaiting();
										uni.setStorageSync("tempUUID", res.result);
										const path =
											`tempUUID=${res.result}`;
										uni.reLaunch({
											url: `/pages/smsVerify/index?${path}`,
										})
									} else {
										uni.showToast({
											title: "身份认证失败",
											icon: "error",
											duration: 2000,
										});
										plus.nativeUI.closeWaiting();
										uni.reLaunch({
											url: "/pages/login/login",
										});
									}
								});
							} else {
								plus.nativeUI.closeWaiting();
								uni.reLaunch({
									url: "/pages/login/login",
								});
							}
						} else {
							
							wv.setStyle({
								width: "100vw",
							});
							wv.setStyle({
								width: "100%",
							});
							setTimeout(() => {
								plus.nativeUI.closeWaiting();
							}, 1000);
						}
					});
				}
			}, 10);
		},
		methods: {
			onSmsVerify(_tgt) {
				const tgt = _tgt;
				const _this = this;
				const cookieIsOk = () => {
					setTimeout(() => {
						uni.setStorageSync("expiresTime", new Date().getTime());
						_this.wv.reload();
					}, 500);
				};
				if (uni.getSystemInfoSync().platform == "ios") {
					nativeSetCookie({
							name: "CASTGC",
							value: tgt,
							domain: "pass.neu.edu.cn",
							path: "/tpass/",
						},
						(success) => {
							if (success) {
								cookieIsOk();
							}
							cookieIsOk();
						}
					);
					cookieIsOk();
				} else {
					plus.navigator.setCookie(
						"https://pass.neu.edu.cn",
						`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
					);
								plus.navigator.setCookie(
									"https://webvpn.neu.edu.cn",
									`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
								);
					cookieIsOk();
				}
			},
			onLoadSuccess() {
				console.log("onLoadSuccess:")
			},
			onMessageClick(e) {
				const type = e.detail.data[0]
				console.log(type)
				if (type == 1) {
					uni.navigateTo({
						url: "/pages/about/about"
					})
				} else if (type == 2) {
					uni.navigateTo({
						url: "/pages/privacy/privacy"
					})
				} else if (type == 3) {
					//退出登录
					const privacy = uni.getStorageSync('privacy')
					plus.navigator.removeAllCookie()
					plus.navigator.removeSessionCookie()
					plus.navigator.removeCookie()
					uni.setStorageSync("tgt", '')
					uni.setStorageSync("privacy", privacy)
					uni.setStorageSync("isLogin", 0)
					
					setTimeout(() => {
						uni.reLaunch({
							url: "/pages/login/login"
						})
					}, 0)
					
				}
			}
		}

	}
</script>

<style scoped>

</style>