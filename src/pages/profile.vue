<template>
	<view>
		<web-view :src="webUrl" @message="onMessageClick" @load="onLoadSuccess"></web-view>
	</view>
</template>

<script>
	export default {
		name: "neunews",
		data() {
			return {
			   webUrl: "https://personal.neu.edu.cn/prize/fe/square/userMy",
			}
		},
		onLoad() {

			var currentWebview = this.$scope.$getAppWebview();
			const timer = setInterval(function() {
				const wv = currentWebview.children()[0]
				if (wv) {
					clearInterval(timer)
					wv.addEventListener('loaded', function(e) {
						console.log("我的页面加载完成____:loaded:")
						uni.setNavigationBarTitle({
							title: " "
						});
						uni.hideLoading()
					})
				}
			}, 10)
		},
		onShow() {


		},
		methods: {
			onLoadSuccess() {
				console.log("onLoadSuccess:")
			},
			onMessageClick(e) {
				const type = e.detail.data[0]
				console.log(type)
				if (type == 1) {
					uni.navigateTo({
						url: "/pages/about/about"
					})
				} else if (type == 2) {
					uni.navigateTo({
						url: "/pages/privacy/privacy"
					})
				} else if (type == 3) {
					//退出登录
					const privacy = uni.getStorageSync('privacy')
					plus.navigator.removeAllCookie()
					plus.navigator.removeSessionCookie()
					plus.navigator.removeCookie()
					uni.setStorageSync("tgt", '')
					uni.setStorageSync("privacy", privacy)
					uni.setStorageSync("isLogin", 0)
					
					setTimeout(() => {
						uni.reLaunch({
							url: "/pages/login/login"
						})
					}, 0)
					
				}
			}
		}

	}
</script>

<style scoped>

</style>