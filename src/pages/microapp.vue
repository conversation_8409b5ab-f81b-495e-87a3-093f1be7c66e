<template>
	<view>
		<web-view :src="webUrl"></web-view>
	</view>
</template>

<script>
	export default {
		name: "microapp",
		data() {
			return {
                webUrl: "https://personal.neu.edu.cn/abilitycenter/fe/site/m_my",
			}
		},
		onLoad() {

            // uni.showLoading({
            // 	title:"加载中..."
            // })
			var currentWebview = this.$scope.$getAppWebview();
			setTimeout(function() {
				var wv = currentWebview.children()[0];
				wv.overrideUrlLoading({
					mode: 'reject'
				}, (e) => {
					uni.navigateTo({
						url: "/pages/webview/appwebview?url=" + encodeURIComponent(e.url)
					})
				})
			}, 2000)
		}
	}
</script>

<style scoped>

</style>