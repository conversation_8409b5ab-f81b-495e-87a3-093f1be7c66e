<template>
  <view>
    <web-view src="https://ecode.neu.edu.cn/ecode/#/" :update-title="false"/>
    <!--    <web-view src="https://ecode.neu.edu.cn/ecode-staging/#/" :update-title="false"/>-->
    <view>
      <tabbar :current="0"></tabbar>
    </view>
  </view>
</template>

<script>
export default {
  name: "ecode",
  onShow() {
    // #ifdef APP-PLUS
    uni.getScreenBrightness({
      success: function (res) {
        uni.setStorageSync('ScreenBrightness', res.value);
        uni.setScreenBrightness({
          value: 1
        })
      }
    })
    if (this.$scope.$getAppWebview().children()[0]) {
      this.$scope.$getAppWebview().children()[0].loadURL('https://ecode.neu.edu.cn/ecode/#/');
      // this.$scope.$getAppWebview().children()[0].loadURL('https://ecode.neu.edu.cn/ecode-staging/#/');
    }
    uni.setUserCaptureScreen({
      enable: false,
      complete: () => {
        console.log("开启防截屏");
      }
    });
    // #endif
    // 隐藏uniapp自带导航
    uni.hideTabBar({
      animation: false
    })
  },
  onHide() {
    // #ifdef APP-PLUS
    let screen = uni.getStorageSync('ScreenBrightness');
    uni.setScreenBrightness({
      value: -1
    })
    this.$scope.$getAppWebview().children()[0].loadURL('about:blank');
    uni.setUserCaptureScreen({
      enable: true,
      complete: () => {
        console.log("关闭防截屏");
      }
    });
    // #endif
  },
  mounted() {
    // #ifdef APP-PLUS
    let screenHeight = uni.getSystemInfoSync().safeArea.height - 67
    let currentWebview = this.$scope.$getAppWebview()
    let wv = currentWebview.children()[0]
    wv.setStyle({height: screenHeight})
    // #endif
  }
}
</script>

<style scoped>

</style>
