<template>
	<view>
		<web-view :src="webUrl" @message="onMessageListen"></web-view>
	</view>
</template>

<script>
	export default {
		onLoad(options) {

			const url = decodeURIComponent(options.url)
			// console.log("url___:",url, options)
			if (url.includes('https://') || url.includes('http://')) {
				this.webUrl = url
			} else {
				const baseDomain = "https://personal.neu.edu.cn"
				const path = baseDomain + url
				this.webUrl = path
			}
		},
		onShow() {
			/*  */
			// uni.showLoading({
			// 	title: "加载中"
			// })

			const _this = this
			var currentWebview = this.$scope.$getAppWebview();
			setTimeout(function() {
				var wv = currentWebview.children()[0];
				// wv.addEventListener('loaded', function(e) {
				// 	console.log("loaded:", wv.getTitle())
				// 	if (title == '日程中心' || title == '新闻资讯' || title == '应用中心' || title == '订阅') {
				// 		const removeTitleJs =
				// 			`document.getElementsByClassName("head")[0].remove()`
				// 		wv.evalJS(removeTitleJs)
				// 	}
				// })
				// setTimeout(function() {
				// 	console.log("title-_:", wv.getTitle())
				// 	const title = wv.getTitle()
				// 	if (title == '日程中心' || title == '新闻资讯' || title == '应用中心' || title == '订阅') {
				// 		const removeTitleJs =
				// 			`document.getElementsByClassName("head")[0].remove()`
				// 		wv.evalJS(removeTitleJs)
				// 	} else if (title.includes('http://') || title.includes("https://")) {
				// 		uni.setNavigationBarTitle({
				// 			title: " "
				// 		});
				// 	}
				// 	uni.hideLoading()
				// }, 2500);
			}, 2000);
		},
		data() {
			return {
				webUrl: ""
			}
		},
		methods: {
			onMessageListen: function(e) {
				console.log("e____:", e)
				// uni.setNavigationBarTitle
				const obj = e.detail.data[0]
				if (obj.type == 'schedulesite' && obj.action == 'save') {

					uni.showToast({
						title: "保存成功",
						icon: "none",
					})
                    uni.navigateBack()
				}

			},

		}
	}
</script>

<style>

</style>