<template>
	<view>
		<web-view :src="webUrl" @message="onMessageListen"></web-view>
	</view>
</template>

<script>
	import {
		login,
		checkVersionInfo,
		sendSmsVerification,
		verifySmsCode,
		getDeviceIdClientIP,
		getLoginType,
		smsSend,
		smsVerify,
		smsLogin,
	} from "@/api/login";
	import {
		request
	} from "@/utils/request";

	import {
		getNumber,
		nativeSetCookie
	} from "@/uni_modules/sync-cookie";
	export default {
		data() {
			return {
				wv: null,
			};
		},
		onLoad(options) {

			const url = decodeURIComponent(options.url)
			if (url.includes('https://') || url.includes('http://')) {
				this.webUrl = url
			} else {
				const baseDomain = "https://personal.neu.edu.cn"
				const path = baseDomain + url
				this.webUrl = path
			}
		},
		onShow() {
			return
			const _this = this;
			const currentWebview = _this.$scope.$getAppWebview();
			// plus.nativeUI.showWaiting();
			const timer = setInterval(function() {
				const wv = currentWebview.children()[0];
				uni.hideKeyboard();
				if (wv) {
					clearInterval(timer);
					wv.addEventListener('loading', (e) => {
						plus.nativeUI.showWaiting()
						wv.setStyle({
							width: "1px",
						});
					});
					_this.wv = wv;
					wv.addEventListener("loaded", function() {
						if (wv.getURL().includes("/tpass/login")) {
							if (wv.getURL().includes("webvpn.neu.edu.cn")) {
								plus.nativeUI.closeWaiting()
								uni.reLaunch({
									url: "/pages/login/login"
								})
								return
							}
							uni.hideKeyboard();
							const userName = uni.getStorageSync("userName");
							const userPsw = uni.getStorageSync("userPsw");
							const rememberPsw = uni.getStorageSync("rememberPsw");
							if (userName && userPsw) {

								const data = {
									username: userName,
									password: userPsw,
								};

								smsLogin(data).then((res) => {

									plus.nativeUI.closeWaiting();
									if (res.code == 1) {
										_this.onSmsVerify(res.result);
									} else if (res.code == 2 || res.code == 4) {
										uni.showToast({
											title: res.msg,
											icon: "none",
										});
									} else if (res.code == 3) {
										plus.nativeUI.closeWaiting();
										uni.setStorageSync("tempUUID", res.result);
										const path =
											`tempUUID=${res.result}`;
										uni.reLaunch({
											url: `/pages/smsVerify/index?${path}`,
										})
									} else {
										uni.showToast({
											title: "身份认证失败",
											icon: "error",
											duration: 2000,
										});
										plus.nativeUI.closeWaiting();
										uni.reLaunch({
											url: "/pages/login/login",
										});
									}
								});
							} else {
								plus.nativeUI.closeWaiting();
								uni.reLaunch({
									url: "/pages/login/login",
								});
							}
						} else {

							wv.setStyle({
								width: "100vw",
							});
							wv.setStyle({
								width: "100%",
							});
							setTimeout(() => {
								plus.nativeUI.closeWaiting();
							}, 1000);
						}
					});
				}
			}, 10);
		},
		methods: {
			onSmsVerify(_tgt) {
				const tgt = _tgt;
				const _this = this;
				const cookieIsOk = () => {
					setTimeout(() => {
						uni.setStorageSync("expiresTime", new Date().getTime());
						_this.wv.reload();
					}, 500);
				};
				if (uni.getSystemInfoSync().platform == "ios") {
					nativeSetCookie({
							name: "CASTGC",
							value: tgt,
							domain: "pass.neu.edu.cn",
							path: "/tpass/",
						},
						(success) => {
							if (success) {
								cookieIsOk();
							}
							cookieIsOk();
						}
					);
					cookieIsOk();
				} else {
					plus.navigator.setCookie(
						"https://pass.neu.edu.cn",
						`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
					);
					cookieIsOk();
				}
			},
			onMessageListen: function(e) {
				
				const obj = e.detail.data[0]
				if (obj.type == 'schedulesite' && obj.action == 'save') {

					uni.showToast({
						title: "保存成功",
						icon: "none",
					})
					uni.navigateBack()
				}

			},

		}
	}
</script>

<style>

</style>