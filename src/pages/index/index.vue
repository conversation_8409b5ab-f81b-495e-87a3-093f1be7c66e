<template>
	<view id="webViewBox">
		<web-view :src="webUrl" @message="onMessageListen"></web-view>
	</view>
</template>

<script>
	import {
		login,
		checkVersionInfo,
		smsLogin
	} from "@/api/login";
	import {
		getNumber,
		nativeSetCookie,
		getNativeWebCookie,
	} from "@/uni_modules/sync-cookie";
	// import {
	// 	registerPushNotifaication
	// } from '@/utils/registerNotificatio.js'
	export default {
		components: {},
		data() {
			return {
				isLoaded: false,
				webUrl: "https://personal.neu.edu.cn/portal/frontend/vspage/project/2",
				// webUrl:"http://**************:8080/prize/fe/square/userMy",
				// webUrl:"https://www.pgyer.com/zhihuidongda",
				wv: null,
			};
		},

		onLoad(options) {
			// 	registerPushNotifaication()
		},
		onHide() {
			console.log("index onHide");
		},
		onShow() {
			const isIOS = uni.getSystemInfoSync().platform == "ios";
			const _this = this;
			const currentWebview = _this.$scope.$getAppWebview();
			plus.nativeUI.showWaiting();
			const timer = setInterval(function() {
				const wv = currentWebview.children()[0];
				uni.hideKeyboard();
				if (wv) {
					
					clearInterval(timer);
					if (_this.isLoaded) {
						wv.reload()
						return
					}
					wv.addEventListener('loading', (e) => {
						plus.nativeUI.showWaiting()
						wv.setStyle({
							width: "1px",
						});
					});
					wv.addEventListener("loaded", function() {
						_this.wv = wv;
						if (wv.getURL().includes("/tpass/login")) {
							if (wv.getURL().includes("webvpn.neu.edu.cn")) {
								plus.nativeUI.closeWaiting()
								uni.reLaunch({
									url: "/pages/login/login"
								})
								return
							}
							uni.hideKeyboard();
							const userName = uni.getStorageSync("userName");
							const userPsw = uni.getStorageSync("userPsw");
							const rememberPsw = uni.getStorageSync("rememberPsw");
							if (userName && userPsw) {
								const data = {
									username: userName,
									password: userPsw,
								};
								smsLogin(data).then((res) => {
									plus.nativeUI.closeWaiting();
									if (res.code == 1) {
										_this.onSmsVerify(res.result);
									} else if (res.code == 2 || res.code == 4) {
										uni.showToast({
											title: res.msg,
											icon: "none",
										});
									} else if (res.code == 3) {
										plus.nativeUI.closeWaiting();
										uni.setStorageSync("tempUUID", res.result);
										const path =
											`tempUUID=${res.result}`;
										uni.reLaunch({
											url: `/pages/smsVerify/index?${path}`,
										})
									} else {
										uni.showToast({
											title: "身份认证失败",
											icon: "error",
											duration: 2000,
										});
										plus.nativeUI.closeWaiting();
										uni.reLaunch({
											url: "/pages/login/login",
										});
									}
								});
							} else {
								plus.nativeUI.closeWaiting();
								uni.reLaunch({
									url: "/pages/login/login",
								});
							}
						} else {
							
							wv.setStyle({
								width: "100vw",
							});
							wv.setStyle({
								width: "100%",
							});
							plus.runtime.getProperty(plus.runtime.appid, (appInfo) => {
								// 获取版本号
								const currentVersion = appInfo.version;
								const systemInfo = uni.getSystemInfoSync();
								// 0 是安卓  1 是iOS
								const os = systemInfo.platform == "android" ? "0" : "1";
								checkVersionInfo({
									os: os,
								}).then( onlineVersionInfo => {
									const latestVersion = onlineVersionInfo.result.number; // 版本号
									
								    if(latestVersion == currentVersion) {
											setTimeout(() => {
												const jsCode = `
												    (function(){
												      var timer = setInterval(function(){
												        var els = document.querySelectorAll('.localSidebarPc');
												        if(els.length){
												          els.forEach(function(el){ el.style.display = 'none'; });
												          clearInterval(timer);
												        }
												      }, 50);
												      // 10 秒后强制结束，防止内存泄漏
												      setTimeout(function(){ clearInterval(timer); }, 10000);
												    })();
												  `;
												  wv.evalJS(jsCode);
											},100)
									}
								})
								
							});
							_this.isLoaded = true;
							setTimeout(() => {
								plus.nativeUI.closeWaiting();
							}, 1000);
						}
					});
				}
			}, 10);
		},
		methods: {
			compareVersion(v1, v2) {
				const version1 = v1.split(".").map(Number);
				const version2 = v2.split(".").map(Number);
			
				for (let i = 0; i < version1.length; i++) {
					if (version1[i] < version2[i]) {
						return -1;
					} else if (version1[i] > version2[i]) {
						return 1;
					}
				}
				return 0;
			},
			onSmsVerify(_tgt) {
				const tgt = _tgt;
				const _this = this;
				const cookieIsOk = () => {
					setTimeout(() => {
						uni.setStorageSync("expiresTime", new Date().getTime());
						_this.wv.reload();
					}, 500);
				};
				if (uni.getSystemInfoSync().platform == "ios") {
					nativeSetCookie({
							name: "CASTGC",
							value: tgt,
							domain: "pass.neu.edu.cn",
							path: "/tpass/",
						},
						(success) => {
							if (success) {
								cookieIsOk();
							}
							cookieIsOk();
						}
					);
					cookieIsOk();
				} else {
					plus.navigator.setCookie(
						"https://pass.neu.edu.cn",
						`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
					);
					cookieIsOk();
				}
			},
			 getOnlineVersion(){
			  
			},
			onMessageListen(e) {
				const type = e.detail.data[0].type;
				const url = e.detail.data[0].url;
				if (type == "scan") {
					this.scan();
				}
			},
			scan() {
				uni.scanCode({
					onlyFromCamera: true,
					scanType: ["qrCode"],
					success: function(res) {
						let result = res.result;
						let src = "http";
						let platform = uni.getSystemInfoSync().platform;
						if (result.startsWith("http") || result.startsWith("https")) {
							if (platform === "ios") {
								setTimeout(() => {
									uni.navigateTo({
										url: "/pages/webview/appwebview?url=" +
											encodeURIComponent(result),
									});
								}, 100);
								return;
							} else {
								uni.navigateTo({
									url: "/pages/webview/appwebview?url=" + encodeURIComponent(result),
								});
							}
						} else {
							uni.showModal({
								content: res.result,
							});
						}
					},
					fail: function(res) {
						console.log("11", res);
					},
				});
			},
		},
	};
</script>

<style>
	/* .coverView{
  position: fixed;
  top: 10rpx;
  right: 0rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: red;
} */
</style>