<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>页面加载失败</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
        }
        .error-container {
            padding: 30px;
            max-width: 80%;
        }
        .error-icon {
            font-size: 80px;
            color: #999;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .error-message {
            font-size: 14px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .retry-btn {
            display: inline-block;
            background-color: #007AFF;
            color: white;
            padding: 10px 30px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 16px;
            border: none;
            outline: none;
            cursor: pointer;
        }
        .retry-btn:active {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <svg width="80" height="80" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z" fill="#FEE2E2" stroke="#EF4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15 15L33 33" stroke="#EF4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15 33L33 15" stroke="#EF4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        <h1 class="error-title">页面加载失败</h1>
        <p class="error-message" id="errorMsg">网络连接异常或页面不存在，请检查网络后重试</p>
        <button class="retry-btn" id="retryBtn">重新加载</button>
    </div>

    <script>
        // 获取错误信息
        document.addEventListener("error", function(e) {
            var url = e.url; // 错误页面的url地址
            var href = e.href; // 错误页面的完整路径（包括完整的协议头）
            
            // 显示具体的错误信息
            var errorMsg = document.getElementById('errorMsg');
            if (url) {
                errorMsg.textContent = '无法加载: ' + url;
            } else if (href) {
                errorMsg.textContent = '无法加载: ' + href;
            }
        }, false);

        // 重试按钮点击事件
        document.getElementById('retryBtn').addEventListener('click', function() {
            // 调用父页面的重载方法
            if (window.parent && window.parent.location) {
                window.parent.location.reload();
            } else if (window.opener && window.opener.location) {
                window.opener.location.reload();
            } else {
                window.location.href = 'about:blank';
                setTimeout(function() {
                    window.location.reload();
                }, 100);
            }
        });
    </script>
</body>
</html>
