<script>
	import {
		user
	} from '@/api/user'

	import {
		login
	} from '@/api/login'
// 3EB43FEE1311DEC4137B87DB230EECF4
//3BA3072F01F5B3663CDD1538CABF9A5F
	// import {
	// 	registerPushNotifaication
	// } from '@/utils/registerNotificatio.js'
	import {
		getNumber,
		nativeSetCookie
	} from '@/uni_modules/sync-cookie'
import { METHODS } from 'http'
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},

		onShow: function() {
			plus.runtime.setBadgeNumber(0)
			const expiresTime = uni.getStorageSync('expiresTime');
			const isLogin = uni.getStorageSync('isLogin') == 1;
			const isSms = uni.getStorageSync("isSms") == 1;
			// registerPushNotifaication()
			if (expiresTime && isLogin) {
				const currentTime = new Date().getTime();
				const timeDifference = currentTime - expiresTime;
				const twoHoursInMilliseconds = 2 * 60 * 60 * 1000;
				if (timeDifference > twoHoursInMilliseconds) {
						//5E696DC93601EEB2137D9E7036F12C93
						//3EB43FEE1311DEC4137B87DB230EECF4
					setTimeout(() => {
						uni.switchTab({
							url: "/pages/index/index",
							})
					}, 10)

				} else {
					console.log('存储时间未超过2小时');
				}
			} else {
				console.log('未找到存储的时间戳');
			}
		},
		onHide: function() {
			console.log('App Hide')
		},
	}
	
</script>

<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "@/uni_modules/uview-ui/index.scss";
	/*每个页面公共css */
	@import './common/uni.css';
</style>