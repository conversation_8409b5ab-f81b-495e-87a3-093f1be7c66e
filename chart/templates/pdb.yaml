{{- if .Values.podDisruptionBudget.enabled }}
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: {{ template "fullname" . }}
  labels:
{{ include "sharedlabels" . | indent 4 }}
spec:
{{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
{{- end }}
{{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
{{- end }}
  selector:
    matchLabels:
      app: {{ template "appname" . }}
      release: {{ .Release.Name }}
{{- end }}
