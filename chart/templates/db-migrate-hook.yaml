{{- if .Values.application.migrateCommand -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "trackableappname" . }}-db-migrate
  labels:
{{ include "sharedlabels" . | indent 4 }}
  annotations:
    "helm.sh/hook": pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation
    "helm.sh/hook-weight": "0"
spec:
  template:
    metadata:
      labels:
        app: {{ template "appname" . }}
        release: {{ .Release.Name }}
    spec:
      restartPolicy: Never
      imagePullSecrets:
{{ toYaml .Values.image.secrets | indent 10 }}
      containers:
      - name: {{ .Chart.Name }}
        image: {{ template "imagename" . }}
        command: ["/bin/sh"]
        args: ["-c", "{{ .Values.application.migrateCommand }}"]
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- if .Values.application.secretName }}
        envFrom:
        - secretRef:
            name: {{ .Values.application.secretName }}
        {{- end }}
        env:
        - name: DATABASE_URL
          value: {{ .Values.application.database_url | quote }}
        - name: GITLAB_ENVIRONMENT_NAME
          value: {{ .Values.gitlab.envName | quote }}
        - name: GITLAB_ENVIRONMENT_URL
          value: {{ .Values.gitlab.envURL | quote }}
{{- end -}}
