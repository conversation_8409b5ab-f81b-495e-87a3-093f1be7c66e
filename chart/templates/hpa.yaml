{{- if and .Values.hpa.enabled .Values.resources.requests -}}
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ template "fullname" . }}
  labels:
{{ include "sharedlabels" . | indent 4 }}
spec:
  scaleTargetRef:
    kind: Deployment
    name: {{ template "appname" . }}
    apiVersion: apps/v1
  minReplicas: {{ .Values.hpa.minReplicas }}
  maxReplicas: {{ .Values.hpa.maxReplicas }}
  targetCPUUtilizationPercentage: {{ .Values.hpa.targetCPUUtilizationPercentage }}
{{- end -}}
