{{- if and (.Values.service.enabled) (or (.Values.ingress.enabled) (not (hasKey .Values.ingress "enabled"))) -}}
{{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
apiVersion: networking.k8s.io/v1
{{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta1/Ingress"}}
apiVersion: networking.k8s.io/v1beta1
{{ else }}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ template "fullname" . }}
  labels:
{{ include "sharedlabels" . | indent 4 }}
  annotations:
{{- if .Values.ingress.annotations }}
{{ toYaml .Values.ingress.annotations | indent 4 }}
{{- end }}
{{- if .Values.ingress.tls.enabled }}
    kubernetes.io/tls-acme: {{ .Values.ingress.tls.acme | quote }}
{{- end }}
{{- if eq .Values.application.track "canary" }}
    nginx.ingress.kubernetes.io/canary: "true"
    nginx.ingress.kubernetes.io/canary-by-header: "canary"
{{- if .Values.ingress.canary.weight }}
    nginx.ingress.kubernetes.io/canary-weight: {{ .Values.ingress.canary.weight | quote }}
{{- end }}
{{- end }}
{{- with .Values.ingress.modSecurity }}
{{- if .enabled }}
    nginx.ingress.kubernetes.io/modsecurity-transaction-id: "$server_name-$request_id"
    nginx.ingress.kubernetes.io/modsecurity-snippet: |
      SecRuleEngine {{ .secRuleEngine | default "DetectionOnly" | title }}
{{- range $rule := .secRules }}
{{ (include "secrule" $rule) | indent 6 }}
{{- end }}
{{- end }}
{{- end }}
{{- if .Values.prometheus.metrics }}
    nginx.ingress.kubernetes.io/server-snippet: |-
      location /metrics {
          deny all;
      }

{{- end }}
spec:
{{- if .Values.ingress.tls.enabled }}
  tls:
  - hosts:
{{- if .Values.service.commonName }}
    - {{ template "hostname" .Values.service.commonName }}
{{- end }}
    - {{ template "hostname" .Values.service.url }}
{{- if .Values.service.additionalHosts }}
{{- range $host := .Values.service.additionalHosts }}
    - {{ template "hostname" $host }}
{{- end -}}
{{- end }}
{{- if not .Values.ingress.tls.useDefaultSecret }}
    secretName: {{ .Values.ingress.tls.secretName | default (printf "%s-tls" (include "fullname" .)) }}
{{- end }}
{{- end }}
  rules:
  - host: {{ template "hostname" .Values.service.url }}
    http:
      &httpRule
      paths:
      - path: {{ .Values.ingress.path | default "/" | quote }}
        {{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
        pathType: Prefix
        {{- end }}
        backend:
          {{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
          service:
            name: {{ template "fullname" . }}
            port:
              number: {{ .Values.service.externalPort }}
          {{ else }}
          serviceName: {{ template "fullname" . }}
          servicePort: {{ .Values.service.externalPort }}
          {{- end }}
{{- if .Values.service.commonName }}
  - host: {{ template "hostname" .Values.service.commonName }}
    http:
      <<: *httpRule
{{- end -}}
{{- if .Values.service.additionalHosts }}
{{- range $host := .Values.service.additionalHosts }}
  - host: {{ template "hostname" $host }}
    http:
      <<: *httpRule
{{- end -}}
{{- end -}}
{{- end -}}
