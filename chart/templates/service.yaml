{{- if .Values.service.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "fullname" . }}
  annotations:
{{- if .Values.service.annotations }}
{{ toYaml .Values.service.annotations | indent 4 }}
{{- end }}
{{- if .Values.prometheus.metrics }}
    prometheus.io/scrape: "true"
    prometheus.io/port: "{{ .Values.service.internalPort }}"
{{- end }}
  labels:
    track: "{{ .Values.application.track }}"
{{ include "sharedlabels" . | indent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
  - port: {{ .Values.service.externalPort }}
    targetPort: {{ .Values.service.internalPort }}
    protocol: TCP
    name: {{ .Values.service.name }}
  selector:
    app: {{ template "appname" . }}
    tier: "{{ .Values.application.tier }}"
    track: "{{ .Values.application.track }}"
{{- end -}}
