# Default values for chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
strategyType:
# `serviceAccountName` is deprecated in favor of `serviceAccount.name`
serviceAccountName:
image:
  repository: gitlab.example.com/group/project
  tag: stable
  pullPolicy: IfNotPresent
  secrets:
    - name: gitlab-registry
extraLabels: {}
lifecycle: {}
# preStop:
#   exec:
#     command: ["/bin/sh", "-c", "sleep 10"]
podAnnotations: {}
nodeSelector: {}
affinity: {}
tolerations: []
application:
  track: stable
  tier: web
  migrateCommand:
  initializeCommand:
  secretName:
  secretChecksum:
hpa:
  enabled: false
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80
gitlab:
  app:
  env:
  envName:
  envURL:
  projectID:
service:
  enabled: true
  annotations: {}
  name: web
  type: ClusterIP
  url: http://my.host.com/
  additionalHosts:
  commonName:
  externalPort: 5000
  internalPort: 5000
ingress:
  enabled: true
  path: "/"
  tls:
    enabled: true
    acme: true
    secretName: ""
    useDefaultSecret: false
  annotations:
    kubernetes.io/ingress.class: "nginx"
  modSecurity:
    enabled: false
    secRuleEngine: "DetectionOnly"
    # secRules:
    #   - variable: ""
    #     operator: ""
    #     action: ""
  canary:
    weight:
prometheus:
  metrics: false
livenessProbe:
  path: "/"
  initialDelaySeconds: 15
  timeoutSeconds: 15
  scheme: "HTTP"
  probeType: "httpGet"
readinessProbe:
  path: "/"
  initialDelaySeconds: 5
  timeoutSeconds: 3
  scheme: "HTTP"
  probeType: "httpGet"
postgresql:
  managed: false
  managedClassSelector:
    #   matchLabels:
    #     stack: gitlab (This is an example. The labels should match the labels on the CloudSQLInstanceClass)

resources:
#  limits:
#    cpu: 100m
#    memory: 128Mi
  requests: {}
#    cpu: 100m
#    memory: 128Mi

## Configure PodDisruptionBudget
## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/
#
podDisruptionBudget:
  enabled: false
  # minAvailable: 1
  maxUnavailable: 1

## Configure NetworkPolicy
## ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
#
networkPolicy:
  enabled: false
  spec:
    podSelector:
      matchLabels: {}
    ingress:
    - from:
      - podSelector:
          matchLabels: {}
      - namespaceSelector:
          matchLabels:
            app.gitlab.com/managed_by: gitlab

ciliumNetworkPolicy:
  enabled: false
  alerts:
    enabled: false
  spec:
    endpointSelector: {}
    ingress:
    - fromEndpoints:
      - matchLabels:
          app.gitlab.com/managed_by: gitlab

serviceAccount:
  name:
  annotations: {}
  createNew: false

workers: {}
  # worker:
  #   replicaCount: 1
  #   terminationGracePeriodSeconds: 60
  #   command:
  #   - /bin/herokuish
  #   - procfile
  #   - start
  #   - worker
  #   nodeSelector: {}
  #   tolerations: []
  #   livenessProbe:
  #     path: "/"
  #     initialDelaySeconds: 15
  #     timeoutSeconds: 15
  #     scheme: "HTTP"
  #     probeType: "httpGet"
  #   readinessProbe:
  #     path: "/"
  #     initialDelaySeconds: 5
  #     timeoutSeconds: 3
  #     scheme: "HTTP"
  #     probeType: "httpGet"
  #   lifecycle:
  #     preStop:
  #       exec:
  #         command: ["/bin/sh", "-c", "sleep 10"]
  #   preStopCommand:
  #   - /bin/herokuish
  #   - procfile
  #   - start
  #   - stop_worker
